@extends('main')
@section('webpage')
  <div class="pt-14 lg:px-8">
   
    <div class="mx-auto max-w-2xl py-32 sm:py-48 lg:py-56">
      
      <div class="text-center">
        <h1 class="text-5xl font-semibold tracking-tight text-balance text-[#6113a9] sm:text-7xl">Start Growing Your Business <span class="underline">Now</span></h1>
        <p class="mt-8 text-lg font-medium text-pretty text-[#6113a9] sm:text-xl/8">Maximize Reach & Boost Revenuewith Smart Marketing Solutions</p>
        <div class="mt-10 flex items-center justify-center gap-x-3">
          <a href="#" class="rounded-md bg-[#ff6d00] px-3.5 py-2.5 font-semibold text-white shadow-xs hover:bg-indigo-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">Free Demo</a>
          <a href="#" class="text-sm/6 rounded-md border border-[#6113a9] px-3.5 py-2.5 font-semibold text-[#6113a9]">Watch Video</a>
        </div>
      </div>
    </div>
   
  </div>
</div>


<div class="flex justify-center items-center my-1 ">
 <section class="px-6 pb-16 flex justify-center">
    <div class="relative w-full max-w-5xl rounded-lg overflow-hidden shadow-lg">
      <!-- Replace with your video -->
      <video autoplay loop muted class="w-full h-full object-cover">
        <source src="{{ asset('Videos/vdo1.mp4') }}" type="video/mp4">
        Your browser does not support the video tag.
      </video>

      <!-- Overlay Content -->
      <div class="absolute inset-0 bg-black bg-opacity-40 flex flex-col items-center justify-center text-center text-white px-6">
        <div class="w-16 h-16 flex items-center justify-center border-2 border-white rounded-full mb-4">
          <span class="text-3xl">➔</span>
        </div>
        <h2 class="text-2xl md:text-3xl font-bold">Expand Business Globally</h2>
        <p class="mt-3 text-sm md:text-base max-w-2xl">
          Extend your reach from local to global by unlocking the potential of our exceptional digital marketing products. Our results speak volumes, showcasing our commitment to delivering personalized, enthusiastic, and tailored services for the success of each client’s business.
        </p>
      </div>
    </div>
  </section>


</div>

<div class="w-full bg-gradient-to-bl from-[#68015c] via-[#2d005d] to-[#7e013b] py-12 lg:py-20 px-6 lg:px-16 my-10">
  <div class="container mx-auto flex flex-col lg:flex-row items-center gap-20 justify-center ">

    <!-- Left Content -->
    <div class="lg:w-1/2 text-center lg:text-right space-y-4">
      <p class="text-3xl md:text-5xl lg:text-6xl font-semibold text-white">From Startups to Scaleups</p>
      <p class="text-4xl md:text-6xl lg:text-7xl font-semibold text-white">15,000+ Businesses</p>
      <p class="text-4xl md:text-6xl lg:text-7xl font-semibold text-white">Trust Rapbooster</p>
      <p class="text-2xl md:text-4xl lg:text-5xl font-semibold text-white">Google</p>

      <!-- Rating -->
      <div class="flex justify-center xs:justify-center sm:justify-center md:justify-center lg:justify-end xl:justify-end 2xl:justify-end items-center gap-2 text-white mt-4">
        <span class="text-2xl md:text-3xl font-medium">5.0</span>
        <div class="flex gap-1">
          <i class="fa-solid fa-star text-yellow-400 text-xl"></i>
          <i class="fa-solid fa-star text-yellow-400 text-xl"></i>
          <i class="fa-solid fa-star text-yellow-400 text-xl"></i>
          <i class="fa-solid fa-star text-yellow-400 text-xl"></i>
          <i class="fa-solid fa-star text-yellow-400 text-xl"></i>
        </div>
      </div>
    </div>

    <!-- Right Image -->
    <div class="flex justify-center lg:justify-end">
      <img class="rounded-3xl max-w-sm md:max-w-md lg:max-w-lg" src="{{ asset('images/img.png') }}" alt="Rapbooster Trusted Businesses">
    </div>

  </div>
</div>






<h3 class="text-5xl font-semibold text-center my-10 text-[#6113a9]">Why Businesses Loves Rapbooster</h3>


<div class="container mx-auto px-6 lg:px-20">
  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 justify-items-center gap-8">

    <!-- Single Card -->
    <div class="w-full  rounded-xl h-64 sm:h-72 md:h-80 xl:h-96 flex flex-col justify-end text-left text-white p-4 sm:p-6 bg-no-repeat bg-center bg-cover"
         style="background-image: url('{{ asset('Images/grid1.png') }}')">
      <p class="text-xl sm:text-2xl xl:text-3xl font-semibold">True Customer Support</p>
      <p class="text-sm sm:text-base xl:text-lg mt-2">
        Sed molestie volutpat ante, nec sollicitudin nunc semper a. Vestibulum volutpat risus vitae lacus mollis, quis tristique dolor ultricies.
      </p>
    </div>

    <!-- Card 1 -->
    <div class="w-full  rounded-xl h-64 sm:h-72 md:h-80 xl:h-96 flex flex-col justify-end text-left text-white p-4 sm:p-6 bg-no-repeat bg-center bg-cover"
         style="background-image: url('{{ asset('Images/grid2.png') }}')">
      <p class="text-xl sm:text-2xl xl:text-3xl font-semibold">Quick Set Up</p>
      <p class="text-sm sm:text-base xl:text-lg mt-2">
        Launch your marketing infrastructure within hours — not weeks. 
        Get up and running fast so you can focus on growth.
      </p>
    </div>

    <!-- Card 2 -->
    <div class="w-full  rounded-xl h-64 sm:h-72 md:h-80 xl:h-96 flex flex-col justify-end text-left text-white p-4 sm:p-6 bg-no-repeat bg-center bg-cover"
         style="background-image: url('{{ asset('Images/grid3.png') }}')">
      <p class="text-xl sm:text-2xl xl:text-3xl font-semibold">Automation Driven</p>
      <p class="text-sm sm:text-base xl:text-lg mt-2">
        Save time and boost productivity with powerful marketing automation tools — 
        from CRM to follow-ups.
      </p>
    </div>

    <!-- Duplicate cards -->
    @for ($i = 0; $i < 6; $i++)
      <div class="w-full  rounded-xl h-64 sm:h-72 md:h-80 xl:h-96 flex flex-col justify-end text-left text-white p-4 sm:p-6 bg-no-repeat bg-center bg-cover"
           style="background-image: url('{{ asset('Images/grid1.png') }}')">
        <p class="text-xl sm:text-2xl xl:text-3xl font-semibold">True Customer Support</p>
        <p class="text-sm sm:text-base xl:text-lg mt-2">
          Sed molestie volutpat ante, nec sollicitudin nunc semper a. Vestibulum volutpat risus vitae lacus mollis, quis tristique dolor ultricies.
        </p>
      </div>
    @endfor

  </div>
</div>




 <!-- customer section -->
 <!-- Customer Section -->
<div class="bg-gradient-to-tr from-[#0b0140] via-[#300143] to-[#8d0048] text-white my-10">
  <div class="container mx-auto px-5 py-28 ">

    <!-- Header with buttons -->
    <div class="flex flex-col lg:flex-row justify-between items-center gap-6">
      <div class="lg:w-3/4 space-y-4 text-center lg:text-left">
        <h1 class="text-4xl md:text-5xl font-bold">Words From Our Customers</h1>
        <p class="text-lg md:text-2xl text-balance">
          Our mission is to transform the way businesses communicate by providing a powerful 
          and easy-to-use platform that helps them connect with customers automatically, 
          effectively, and across all digital channels to achieve growth in less time.
        </p>
      </div>
      <div class="flex space-x-4">
        <a href="#slide1" class="btn btn-xl btn-circle border-2 text-white border-white bg-transparent hover:bg-white hover:text-[#540122]">
          <i class="fa-solid fa-arrow-left"></i>
        </a>
        <a href="#slide2" class="btn btn-xl btn-circle border-2 text-white border-white bg-transparent hover:bg-white hover:text-[#540122]">
          <i class="fa-solid fa-arrow-right"></i>
        </a>
      </div>
    </div>

    <!-- Carousel -->
    <div class="carousel w-full mt-16">

      <!-- Slide 1 -->
      <div id="slide1" class="carousel-item relative w-full flex-col sm:flex-col md:flex-row lg:flex-row xl:flex-row flex gap-10">
        
        <!-- Testimonial Card -->
        <div class="flex-1 space-y-9">
          <div class="text-yellow-400 text-2xl">
            <i class="fa-solid fa-star"></i>
            <i class="fa-solid fa-star"></i>
            <i class="fa-solid fa-star"></i>
            <i class="fa-solid fa-star"></i>
            <i class="fa-solid fa-star"></i>
          </div>
          <p class="text-balance text-sm">This tool completely transformed how we run our business – the results were instant and measurable!</p>
          <div>
          <p class="text-xl font-semibold">Rakesh Puniya</p>
          <p class="text-base">Marketing Head, TechTrove</p>
          </div>
        </div>

        <div class="divider divider-horizontal  before:bg-white after:bg-white text-white "></div>

              <!-- Testimonial Card -->
        <div class="flex-1 space-y-9">
          <div class="text-yellow-400 text-2xl">
            <i class="fa-solid fa-star"></i>
            <i class="fa-solid fa-star"></i>
            <i class="fa-solid fa-star"></i>
            <i class="fa-solid fa-star"></i>
            <i class="fa-solid fa-star"></i>
          </div>
          <p class="text-balance text-sm">This tool completely transformed how we run our business – the results were instant and measurable!</p>
          <div>
          <p class="text-xl font-semibold">Rakesh Puniya</p>
          <p class="text-base">Marketing Head, TechTrove</p>
          </div>
        </div>

        <div class="divider divider-horizontal  before:bg-white after:bg-white text-white "></div>
        <!-- Testimonial Card -->
        <div class="flex-1 space-y-9">
          <div class="text-yellow-400 text-2xl">
            <i class="fa-solid fa-star"></i>
            <i class="fa-solid fa-star"></i>
            <i class="fa-solid fa-star"></i>
            <i class="fa-solid fa-star"></i>
            <i class="fa-solid fa-star"></i>
          </div>
          <p class="text-balance text-sm">Absolutely love it! Our team became more productive within a week of using it.</p>
          <div>
          <p class="text-xl font-semibold">Shivam Puniya</p>
          <p class="text-base">Marketing Head, TechTrove</p>
          </div>
        </div>

      </div>

      <!-- Slide 2 -->
      <div id="slide2" class="carousel-item relative w-full flex flex-col sm:flex-col md:flex-row lg:flex-row xl:flex-row gap-10">
        
        <!-- Testimonial Card -->
        <div class="flex-1 space-y-9">
          <div class="text-yellow-400 text-2xl">
            <i class="fa-solid fa-star"></i>
            <i class="fa-solid fa-star"></i>
            <i class="fa-solid fa-star"></i>
            <i class="fa-solid fa-star"></i>
            <i class="fa-solid fa-star"></i>
          </div>
          <p class="text-balance text-sm">The automation saved us hours every week – highly recommend it!</p>
          <div>
          <p class="text-xl font-semibold">Puniya</p>
          <p class="text-base">Marketing Head, TechTrove</p>
          </div>
        </div>

        <div class="divider divider-horizontal  before:bg-white after:bg-white text-white "></div>
        <!-- Testimonial Card -->
        <div class="flex-1 space-y-9">
          <div class="text-yellow-400 text-2xl">
            <i class="fa-solid fa-star"></i>
            <i class="fa-solid fa-star"></i>
            <i class="fa-solid fa-star"></i>
            <i class="fa-solid fa-star"></i>
            <i class="fa-solid fa-star"></i>
          </div>
          <p class="text-balance text-sm">One of the best investments we’ve made for our marketing team.</p>
          <div>
          <p class="text-xl font-semibold">Rahul Sharma</p>
          <p class="text-base">Founder, GrowthEdge</p>
          </div>
        </div>

      </div>

    </div>
  </div>
</div>

<!-- slider section close -->


<!-- slider section -->


<!-- Marketing Tools -->

<!-- Marketing Tools -->
<div class="bg-image" style="background-image: url('{{ asset('Images/bg.png') }}'); background-size: contain; background-position: center; background-repeat: no-repeat;">
  <div class="container mx-auto py-5">
    <p class="text-center text-5xl font-bold text-[#6113a9]">
      Our suite of cost-effective, high-value Tools / Services
    </p>
    <p class="text-center mt-20 text-4xl font-bold text-[#6113a9]">
      Marketing Tools
    </p>
    <p class="text-center text-xl mt-4 mb-12">
      Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. 
      Quis ipsum suspendisse ultrices gravida. Risus commodo viverra maecenas accumsan lacus vel facilisis.
    </p>

    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 my-5 justify-items-center">
      <div class="bg-[#148b3b]  hover:-translate-y-5 py-4 px-6 rounded-xl text-white space-y-9 transition duration-500 ease-out w-80 h-56 hover:shadow-xl/20">
        <p class="text-4xl font-semibold">WhatsApp Marketing</p>
        <div class="flex justify-end mt-4">
          <img src="{{ asset('Images/logo2.png') }}" alt="WhatsApp Marketing">
        </div>
      </div>

      <div class="bg-[#c82f2f]  hover:-translate-y-5 py-4 px-6 rounded-xl text-white space-y-9 transition duration-500 ease-out w-80 h-56 hover:shadow-xl/20">
        <p class="text-4xl font-semibold">Email Marketing</p>
        <div class="flex justify-end">
          <img src="{{ asset('Images/logo2.png') }}" alt="Email Marketing">
        </div>
      </div>

      <div class="bg-[#3965f9]  hover:-translate-y-5 py-4 px-6 rounded-xl text-white space-y-9 transition duration-500 ease-out w-80 h-56 hover:shadow-xl/20">
        <p class="text-4xl font-semibold">SMS <br>Marketing</p>
        <div class="flex justify-end">
          <img src="{{ asset('Images/logo2.png') }}" alt="SMS Marketing">
        </div>
      </div>

      <div class="bg-[#f57c1f] transition duration-500 ease-out hover:-translate-y-5 py-4 px-6 rounded-xl text-white space-y-9 w-80 h-56 hover:shadow-xl/20  ">
        <p class="text-4xl font-semibold">Lead Generation</p>
        <div class="flex justify-end">
          <img src="{{ asset('Images/logo2.png') }}" alt="Lead Generation">
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Marketing Services -->
<div class="container mx-auto mb-20 py-5">
  <p class="text-center text-4xl font-bold text-[#6113a9]">Marketing Services</p>
  <p class="text-center text-xl mt-4 mb-12 ">
    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
  </p>

  <div class="grid grid-cols-1 justify-items-center md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-10">
    <div class="border border-gray-400 py-4 px-6 rounded-xl shadow space-y-8 min-w-80 min-h-56 max-w-80 max-h-56 ">
      <p class="text-4xl font-medium text-red-500">S<span class="text-yellow-400">E</span><span class="text-green-500">O</span></p>
      <div class="flex justify-end">
        <img src="{{ asset('Images/seo.png') }}" alt="SEO">
      </div>
    </div>

    <div class="border border-gray-400 py-4 px-6 rounded-xl shadow space-y-3 min-w-80 min-h-56 max-w-80 max-h-56">
      <p class="text-4xl font-medium"><span class="text-blue-700">Social</span> <span class="text-[#d91677]">Media Marketing</span></p>
      <div class="flex justify-end">
        <img src="{{ asset('Images/seo2.png') }}" alt="Social Media Marketing">
      </div>
    </div>

    <div class="border border-gray-400 py-4 px-6 rounded-xl shadow space-y-3 min-w-80 min-h-56 max-w-80 max-h-56">
      <p class="text-4xl font-medium text-blue-600">Content <span class="text-yellow-400">Marketing</span></p>
      <div class="flex justify-end">
        <img src="{{ asset('Images/seo3.png') }}" alt="Content Marketing">
      </div>
    </div>

    <div class="border border-gray-400 py-4 px-6 rounded-xl shadow space-y-8 min-w-80 min-h-56 max-w-80 max-h-56">
      <p class="text-4xl font-medium text-red-500">Google <span class="text-blue-500">Ads</span></p>
      <div class="flex justify-end">
        <img src="{{ asset('Images/seo4.png') }}" alt="Google Ads">
      </div>
    </div>

    <div class="border border-gray-400 py-4 px-6 rounded-xl shadow space-y-8 min-w-80 min-h-56 max-w-80 max-h-56">
      <p class="text-4xl font-medium text-blue-600">Facebook Ads</p>
      <div class="flex justify-end">
        <img src="{{ asset('Images/facebook.png') }}" alt="Facebook Ads">
      </div>
    </div>

    <div class="border border-gray-400 py-4 px-6 rounded-xl shadow space-y-8  min-w-80 min-h-56 max-w-80 max-h-56">
      <p class="text-4xl font-medium text-fuchsia-600">Instagram Ads</p>
      <div class="flex justify-end">
        <img src="{{ asset('Images/insta.png') }}" alt="Instagram Ads">
      </div>
    </div>

    <div class="border border-gray-400 py-4 px-6 rounded-xl shadow space-y-8 min-w-80 min-h-56 max-w-80 max-h-56">
      <p class="text-4xl font-medium text-red-500">YouTube Ads</p>
      <div class="flex justify-end">
        <img src="{{ asset('Images/youtube.png') }}" alt="YouTube Ads">
      </div>
    </div>

    <div class="border border-gray-400 min-w-80 py-4 px-6 rounded-xl shadow space-y-8 min-h-56 max-w-80 max-h-56">
      <p class="text-4xl font-medium text-[#0b69c7]">LinkedIn Ads</p>
      <div class="flex justify-end">
        <img src="{{ asset('Images/linkedin.png') }}" alt="LinkedIn Ads">
      </div>
    </div>
  </div>
</div>

<!-- Web Development Services -->
<div class="container mx-auto px-6 lg:px-24 py-10">
  <p class="text-center text-4xl font-bold text-[#6113a9]">Web Development Services</p>
  <p class="text-center text-xl mt-4 mb-12">
    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
  </p>

  <div class="flex flex-col lg:flex-row justify-center items-center gap-10  ">
    <div class="border border-gray-400 py-4 px-6 rounded-xl min-w-80 min-h-56 space-y-10 max-w-80 max-h-56">
      <p class="text-[#6113a9] text-4xl max-w-[14rem]">Web Design</p>
      <div class="flex justify-end">
        <img src="{{ asset('Images/web.png') }}" alt="Web Design">
      </div>
    </div>

    <div class="border border-gray-400 py-4 px-6 rounded-xl space-y-3 min-h-56 min-w-80 max-w-80 max-h-56">
      <p class="text-[#6113a9] text-4xl max-w-[14rem]">E-Commerce Website</p>
      <div class="flex justify-end">
        <img src="{{ asset('Images/shop.png') }}" alt="E-Commerce Website">
      </div>
    </div>
  </div>
</div>

<!-- Creative Services -->
<div class="container mx-auto px-6 lg:px-24 py-10">
  <p class="text-center text-4xl font-bold text-[#6113a9]">Creative Services</p>
  <p class="text-center text-xl mt-4 mb-12">
    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
  </p>

  <div class="flex flex-col lg:flex-row items-center justify-center gap-10 my-5">

    <div class="border border-gray-400 py-4 px-6 rounded-xl space-y-4 min-w-80 min-h-56 max-w-80 max-h-56">
      <p class="text-[#6113a9] text-4xl max-w-[12rem]">Graphic Design</p>
      <div class="flex justify-end">
        <img src="{{ asset('Images/pantone.png') }}" alt="Graphic Design">
      </div>
    </div>

    <div class="border border-gray-400 py-4 px-6 rounded-xl space-y-4 min-w-80 min-h-56 max-w-80 max-h-56">
      <p class="text-[#6113a9] text-4xl max-w-[12rem]">Content Writing</p>
      <div class="flex justify-end">
        <img src="{{ asset('Images/content-writing.png') }}" alt="Content Writing">
      </div>
    </div>

    <div class="border border-gray-400 py-4 px-6 rounded-xl space-y-4 min-w-80 min-h-56 max-w-80 max-h-56">
      <p class="text-[#6113a9] text-4xl max-w-[12rem]">Video Production</p>
      <div class="flex justify-end">
        <img src="{{ asset('Images/footage.png') }}" alt="Video Production">
      </div>
    </div>
  </div>
</div>

<!-- Mobile App Development Services -->
<div class="container mx-auto px-6 lg:px-24 py-10">
  <p class="text-center text-4xl font-bold text-[#6113a9] ">Mobile App Development Services</p>
  <p class="text-center text-xl mt-4 mb-12">
    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
  </p>

  <div class="flex flex-col lg:flex-row justify-center items-center gap-10 my-5 ">
    <div class="border border-gray-400 py-4 px-6 rounded-xl space-y-2 min-w-80 min-h-56 max-w-80 max-h-56">
      <p class="text-[#6113a9] text-4xl max-w-[12rem]">Android</p>
      <div class="flex justify-end">
        <img src="{{ asset('Images/android.png') }}" alt="Android">
      </div>
    </div>

    <div class="border border-gray-400 py-4 px-6 rounded-xl space-y-2 min-w-80 min-h-56 max-w-80 max-h-56">
      <p class="text-[#6113a9] text-4xl max-w-[12rem]">iOS</p>
      <div class="flex justify-end">
        <img src="{{ asset('Images/apple.png') }}" alt="iOS">
      </div>
    </div>
  </div>
</div>

<!-- Marketing Tools close -->


<!-- Customer Support Tools -->
<div class="container mx-auto my-20">
  <div class="bg-image p-2 w-full rounded-xl" style="background-image: url('Images/cs.png'); background-size: cover; background-position: center; background-repeat: no-repeat;">
    <div class="w-1/2 sm:w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/3  sm:h-2/4   bg-white rounded-xl p-4  sm:p-4 md:p-6 lg:p-8 xl:p-8 space-y-8">
       <p class="text-2xl sm:text-3xl md:text-4xl lg:text-5xl text-[#6113a9] font-bold leading-snug">
           Fast, Friendly, and Focused on Your Success.
        </p>

        <p class="text-lg sm:text-xl md:text-2xl lg:text-3xl text-[#6113a9] font-semibold mt-4">
            Our Dedicated Team is here <span class="font-bold">6 Days a Week</span> to solve all your queries.
        </p>
        <button class="btn btn-warning btn-sm sm:btn-sm md:btn-md lg:btn-lg xl:btn-xl text-nowrap"><i class="fa-solid fa-comments"></i>Talk to human</button>
    </div>
  </div>
 </div>
<!-- Customer Support Tools close -->
@endsection
